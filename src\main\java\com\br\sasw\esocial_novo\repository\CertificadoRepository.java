package com.br.sasw.esocial_novo.repository;

import com.br.sasw.esocial_novo.util.Certificado;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

@Repository
public class CertificadoRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public Certificado buscarPorEmpresa(String empresa) {
        String sql = "SELECT pathCertificado, senhaConexao FROM Satellite.dbo.esocialcertif WHERE bancoDeDados = ?";

        List<Certificado> resultados = jdbcTemplate.query(sql, new Object[]{empresa.toUpperCase()}, (rs, rowNum) ->
                new Certificado(rs.getString("pathCertificado"), rs.getString("senhaConexao"))
        );

        System.err.println("Size: " + resultados.size());

        if (resultados.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Certificado não encontrado para empresa: " + empresa);
        }

        return resultados.get(0);
    }
}
