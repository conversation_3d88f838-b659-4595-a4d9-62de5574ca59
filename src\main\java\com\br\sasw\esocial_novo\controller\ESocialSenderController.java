package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.esocial.EsocialService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.br.sasw.esocial_novo.esocial.EsocialService.FullResponse;

@RestController
@RequiredArgsConstructor
public class ESocialSenderController {

    private final EsocialService esocialService;

    @PostMapping(path = "/envio-esocial")
    private ResponseEntity<FullResponse> send(@RequestBody String xml,
                                        String id,
                                        @RequestParam String empresa,
                                        @RequestParam String nrInscEmpregador,
                                        @RequestParam String tpInscEmpregador,
										@RequestParam String nrInscTransmissor,
                                        @RequestParam String tpInscTransmissor,
										@RequestParam Integer idGrupo){


        return ResponseEntity.ok().body(esocialService.send(xml, id, empresa, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor, tpInscTransmissor, idGrupo));
    }

    @PostMapping(path = "/consulta-esocial")
    public ResponseEntity<String> get(@RequestParam String protocolo, @RequestParam String empresa) {

        return ResponseEntity.ok().body(esocialService.get(protocolo, empresa));
    }
}
