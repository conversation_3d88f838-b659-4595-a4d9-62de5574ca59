package com.br.sasw.esocial_novo.util;


import com.br.sasw.esocial_novo.repository.CertificadoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CarregarCertificados {

    private static CertificadoRepository certificadoRepository;

    @Autowired
    public CarregarCertificados(CertificadoRepository repo) {
        CarregarCertificados.certificadoRepository = repo;
    }

    public static Certificado buscaCertificadoPorChaveEmpresa(String empresa) {
       return certificadoRepository.buscarPorEmpresa(empresa);
    }
}
