package com.br.sasw.esocial_novo.util;


import java.util.Map;

public class CarregarCertificados {

    public static Certificado buscaCertificadoPorChaveEmpresa(String empresa) {

        Map<String, Certificado> certificados = Map.of(
                "FEDERAL", new Certificado("C:\\certificados-chaves\\FEDERAL\\FEDERAL_GOIANIA.pfx", "Federallcom13", "go-federal.pfx"),
                "INVIOSEG", new Certificado("C:\\certificados-chaves\\INVIOSEG\\INVIOSEG.pfx", "1234", "invioseg.pfx")
        );

        return certificados.get(empresa);
    }
}
