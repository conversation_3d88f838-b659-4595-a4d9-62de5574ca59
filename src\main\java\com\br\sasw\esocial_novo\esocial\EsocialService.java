package com.br.sasw.esocial_novo.esocial;

import br.gov.esocial.servicos.empregador.lote.eventos.envio.v1_1_0.EnviarLoteEventosResponse;
import com.br.sasw.esocial_novo.client.EsocialClient;
import com.br.sasw.esocial_novo.util.CarregarCertificados;
import com.br.sasw.esocial_novo.util.Certificado;
import com.br.sasw.esocial_novo.util.XmlSigner;
import lombok.RequiredArgsConstructor;
import lombok.Data;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HttpProcessor;
import org.apache.http.protocol.HttpProcessorBuilder;
import org.apache.http.protocol.RequestTargetHost;
import org.apache.http.protocol.RequestUserAgent;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import javax.net.ssl.SSLContext;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;

@Service
@RequiredArgsConstructor
public class EsocialService {

    private final EsocialClient esocialClient;
	
	@Data
	public class FullResponse {
		
		private EnviarLoteEventosResponse evento;
		private String xmlEnviado;
	}

    public FullResponse send(String xml, String id, String empresa, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor, Integer idGrupo){
        String signedXml = XmlSigner.sign(xml, empresa);
        String eventoCompleto = buildEvent(signedXml, id, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor,tpInscTransmissor, idGrupo);
        EnviarLoteEventosResponse response = esocialClient.enviarLoteEventos(eventoCompleto);
		FullResponse res = new FullResponse();
		res.setEvento(response);
		res.setXmlEnviado(eventoCompleto);
        return res;
    }

    private String buildEvent(String xml, String id, String nrInscEmpregador, String tpInscEmpregador, String nrInscTransmissor, String tpInscTransmissor, Integer idGrupo){
        String evento =
                """
                 <eSocial xmlns="http://www.esocial.gov.br/schema/lote/eventos/envio/v1_1_1">
                     <envioLoteEventos grupo="${idGrupo}">
                         <ideEmpregador>
                             <tpInsc>${tpInscEmpregador}</tpInsc>
                             <nrInsc>${nrInscEmpregador}</nrInsc>
                         </ideEmpregador>
                         <ideTransmissor>
                             <tpInsc>${tpInscTransmissor}</tpInsc>
                             <nrInsc>${nrInscTransmissor}</nrInsc>
                         </ideTransmissor>
                         <eventos>
                             <evento Id="${id}">${evento}</evento>
                         </eventos>
                 </envioLoteEventos>
                </eSocial>
                """;
        evento = evento.replace("${evento}", xml);
        evento = evento.replace("${id}", id);
        evento = evento.replace("${nrInscEmpregador}", nrInscEmpregador);
        evento = evento.replace("${tpInscEmpregador}", tpInscEmpregador);
		evento = evento.replace("${nrInscTransmissor}", nrInscTransmissor);
        evento = evento.replace("${tpInscTransmissor}", tpInscTransmissor);
		evento = evento.replace("${idGrupo}", String.valueOf(idGrupo));
		
		System.err.println(evento);
        return evento;
    }

    public String get(String protocolo, String empresa) {

        String xml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:v1=\"http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_1_0\">\n" +
                "    <soapenv:Header/>\n" +
                "    <soapenv:Body>\n" +
                "        <v1:ConsultarLoteEventos>\n" +
                "            <v1:consulta>\n" +
                "                <v1:protocoloEnvio>\n" +
                "                    <eSocial xmlns=\"http://www.esocial.gov.br/schema/lote/eventos/envio/consulta/retornoProcessamento/v1_0_0\">\n" +
                "                        <consultaLoteEventos>\n" +
                "                            <protocoloEnvio>{protocolo}</protocoloEnvio>\n" +
                "                        </consultaLoteEventos>\n" +
                "                    </eSocial>\n" +
                "                </v1:protocoloEnvio>\n" +
                "            </v1:consulta>\n" +
                "        </v1:ConsultarLoteEventos>\n" +
                "    </soapenv:Body>\n" +
                "</soapenv:Envelope>";

        xml = xml.replace("{protocolo}", protocolo);

        Certificado certificado = CarregarCertificados.buscaCertificadoPorChaveEmpresa(empresa);

        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new ClassPathResource(certificado.getClassPath()).getInputStream(), certificado.getSenha().toCharArray());

            SSLContext sslContext = SSLContextBuilder.create()
                    .loadKeyMaterial(keyStore, certificado.getSenha().toCharArray())
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();

            HttpProcessor httpProcessor = HttpProcessorBuilder.create()
                    .add(new RequestTargetHost())
                    .add(new RequestUserAgent("Spring WS"))
                    .build();

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext))
                    .setHttpProcessor(httpProcessor)
                    .disableContentCompression()
                    .build();

            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost("https://webservices.consulta.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc");
            httpPost.setHeader("SOAPAction", "http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_1_0/ServicoConsultarLoteEventos/ConsultarLoteEventos");
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            byte[] xmlBytes = xml.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));
            StringEntity entity = new StringEntity(xml, "UTF-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse resp = httpClient.execute(httpPost);

            return resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";


        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
